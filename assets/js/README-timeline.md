# SEVO Timeline System

## Overview
The About Us page features an interactive horizontal timeline that displays SEVO's key milestones and achievements. The timeline is data-driven and easily maintainable.

## File Structure

### 1. `history-data.js`
Contains all timeline data in a structured format:
- **SEVO_TIMELINE_DATA**: Main data object containing all timeline events
- **generateTimelineHTML()**: Function to convert data to HTML
- **initializeTimelineWithData()**: Function to populate the timeline with data

### 2. `about-us.js`
Main timeline functionality:
- Interactive navigation (prev/next arrows)
- Click events on timeline points
- Keyboard navigation (left/right arrows)
- Touch/swipe support for mobile
- Smooth animations and transitions

### 3. `about-us.css`
Styling for both the About Us page and timeline:
- SEVO theme integration (brown, cream, orange colors)
- Responsive design
- Hover effects and animations
- Custom CSS arrows for navigation

## Data Structure

Each timeline event has the following properties:
```javascript
{
    id: 1,                          // Unique identifier
    date: "01/01/2018",            // Date in DD/MM/YYYY format
    displayDate: "2018",           // Short display text
    title: "Event Title",          // Main heading
    subtitle: "January 2018",      // Subheading with date
    description: "Event description...", // Full description
    selected: true/false           // Whether this is the default selected event
}
```

## Adding New Timeline Events

1. Open `assets/js/history-data.js`
2. Add a new event object to the `SEVO_TIMELINE_DATA.events` array
3. Follow the existing data structure
4. The timeline will automatically update

## Customization

### Colors
Timeline colors are controlled by CSS custom properties in `main.css`:
- `--sevo-brown`: #5A3A28
- `--sevo-cream`: #F5EFE6  
- `--sevo-orange`: #FF8C42

### Responsive Behavior
- Desktop: Full horizontal timeline with navigation arrows
- Mobile: Swipe gestures enabled, simplified layout

## Dependencies
- jQuery 3.6.0+ (loaded from CDN)
- SEVO main.css (for theme variables)

## Browser Support
- Modern browsers with CSS Grid and Flexbox support
- IE11+ (with some limitations)
- Mobile browsers (iOS Safari, Chrome Mobile)
