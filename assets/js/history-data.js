// SEVO Timeline History Data
const SEVO_TIMELINE_DATA = {
    events: [
        {
            id: 1,
            date: "01/01/2018",
            displayDate: "2018",
            title: "Foundation and First Steps",
            subtitle: "January 2018",
            description: "SEVO was officially established with a mission to empower women and support displaced communities in South Darfur. Our first initiative focused on providing basic literacy training to 50 women in rural areas, laying the foundation for our comprehensive community development approach.",
            selected: true
        },
        {
            id: 2,
            date: "15/03/2020",
            displayDate: "2020",
            title: "Healthcare Initiative Launch",
            subtitle: "March 2020",
            description: "Launched our mobile health clinic program, providing essential medical services to remote communities. We successfully treated over 1,200 patients in our first year of operation, bringing healthcare directly to those who needed it most during challenging times.",
            selected: false
        },
        {
            id: 3,
            date: "10/06/2021",
            displayDate: "2021",
            title: "Educational Expansion",
            subtitle: "June 2021",
            description: "Opened three community learning centers, providing education and vocational training to over 300 children and adults. Our programs now include computer literacy, traditional crafts, and sustainable agriculture, creating pathways to economic independence.",
            selected: false
        },
        {
            id: 4,
            date: "25/09/2022",
            displayDate: "2022",
            title: "Community Infrastructure Development",
            subtitle: "September 2022",
            description: "Expanded our infrastructure development projects, focusing on improving access to clean water and sanitation facilities. This year marked significant progress in our efforts to address basic needs and improve quality of life in underserved communities.",
            selected: false
        },
        {
            id: 5,
            date: "12/04/2023",
            displayDate: "2023",
            title: "Water and Sanitation Project",
            subtitle: "April 2023",
            description: "Completed the construction of 15 water wells and sanitation facilities, providing clean water access to over 2,000 community members. This project significantly reduced waterborne diseases in the region and improved overall community health outcomes.",
            selected: false
        },
        {
            id: 6,
            date: "08/11/2024",
            displayDate: "2024",
            title: "Women's Empowerment Program",
            subtitle: "November 2024",
            description: "Launched our comprehensive women's empowerment initiative, providing microfinance opportunities, business training, and leadership development to over 500 women across South Darfur. This program represents our commitment to sustainable economic development and gender equality.",
            selected: false
        }
    ]
};

// Function to generate timeline HTML from data
function generateTimelineHTML() {
    const timelineEventsHTML = SEVO_TIMELINE_DATA.events.map(event => 
        `<li><a href="#0" data-date="${event.date}" ${event.selected ? 'class="selected"' : ''}>${event.displayDate}</a></li>`
    ).join('\n\t\t\t\t\t\t');

    const timelineContentHTML = SEVO_TIMELINE_DATA.events.map(event => 
        `<li ${event.selected ? 'class="selected"' : ''} data-date="${event.date}">
\t\t\t\t<h2>${event.title}</h2>
\t\t\t\t<em>${event.subtitle}</em>
\t\t\t\t<p>${event.description}</p>
\t\t\t</li>`
    ).join('\n\n\t\t\t');

    return {
        events: timelineEventsHTML,
        content: timelineContentHTML
    };
}

// Function to initialize timeline with data
function initializeTimelineWithData() {
    const timelineHTML = generateTimelineHTML();
    
    // Update events list
    const eventsContainer = document.querySelector('.cd-horizontal-timeline .events ol');
    if (eventsContainer) {
        eventsContainer.innerHTML = timelineHTML.events;
    }
    
    // Update content list
    const contentContainer = document.querySelector('.cd-horizontal-timeline .events-content ol');
    if (contentContainer) {
        contentContainer.innerHTML = timelineHTML.content;
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SEVO_TIMELINE_DATA, generateTimelineHTML, initializeTimelineWithData };
}
