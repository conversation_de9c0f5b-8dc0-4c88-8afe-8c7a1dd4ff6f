/* SEVO Theme Variables */
:root {
    --sevo-brown: #5A3A28;
    --sevo-cream: #F5EFE6;
    --sevo-orange: #FF8C42;
    --sevo-green: #16A34A;
    --sevo-blue: #2563EB;
    --sevo-purple: #9333EA;
    --text-dark: #333333;
    --text-light: #666666;
    --white: #ffffff;
    --shadow: rgba(90, 58, 40, 0.1);
}

/* General Body Styles */
body {
    margin: 0;
    font-family: 'Inter', 'Poppins', sans-serif;
    background-color: var(--sevo-cream);
    color: var(--text-dark);
    line-height: 1.6;
}

/* Navbar Styles */
.navbar {
    position: sticky;
    top: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fcebd8;
    padding: 1rem 2rem;
    z-index: 1000;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.navbar .logo img {
    height: 50px;
}

.navbar .menu {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center; /* Center the menu items */
    flex-grow: 1; /* Allow the menu to grow and occupy space */
}

.navbar .menu li {
    margin: 0 1.5rem; /* Adjusted for centered layout */
}

.navbar .menu a {
    text-decoration: none;
    color: #5a3e2a;
    font-weight: bold;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.navbar .menu a:hover {
    color: #c6895c;
}

/* Hero Section Styles */
.hero {
    background-color: #c6895c;
    color: #fcebd8;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    padding: 1.6rem 5rem;
    min-height: 80vh;
}

.hero-text {
    padding-right: 3rem;
}

.hero-text h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
}

.hero-text p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.hero-text .btn {
    background-color: #fcebd8;
    color: #c6895c;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    transition: transform 0.3s ease, background-color 0.3s ease;
    border: 2px solid transparent;
}

.hero-text .btn:hover {
    transform: scale(1.05);
    background-color: #ffffff;
    color: #b57a50;
}

.hero-image img {
    max-width: 100%;
    height: auto;
}

/* About Us Section */
.about-us-section {
    background-color: #FDF8F0;
    padding: 100px 0;
    font-family: 'Inter', sans-serif;
    min-height: 65vh;
    display: flex;
    align-items: center;
}

.about-us-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

.about-us-title {
    text-align: center;
    font-family: 'Lora', serif;
    font-size: 3rem;
    color: #333333;
    margin-bottom: 60px;
}

.about-us-content {
    display: flex;
    gap: 60px;
    align-items: center;
}

.about-us-left {
    width: 40%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.about-us-left h2 {
    font-family: 'Lora', serif;
    font-size: 2.5rem;
    color: #333333;
    margin-bottom: 25px;
}

.about-us-left p {
    color: #5D5D5D;
    line-height: 1.7;
    margin-bottom: 35px;
    font-size: 1.2rem;
}

.about-us-button {
    background-color: #fcebd8;
    color: #c6895c;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    transition: transform 0.3s ease, background-color 0.3s ease;
    border: 2px solid transparent;
    align-self: flex-start;
}

.about-us-button:hover {
    transform: scale(1.05);
    background-color: #ffffff;
    color: #b57a50;
}

.about-us-right {
    width: 60%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.focus-area-item {
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.focus-area-item:hover {
    transform: scale(1.05);
}

.focus-area-item:hover .focus-area-label {
    color: #333333;
}

.focus-icon {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    flex-shrink: 0;
}

.focus-area-label {
    color: #5D5D5D;
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

/* Feature Sections */
.feature-section {
    padding: 100px 0;
    min-height: 65vh;
    display: flex;
    align-items: center;
}

.feature-section .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

.feature-content {
    display: flex;
    gap: 60px;
    align-items: center;
}

.feature-text {
    flex: 1;
}

.feature-image {
    flex: 1;
}

.feature-image img {
    width: 100%;
    border-radius: 12px;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.feature-text h2 {
    font-family: 'Lora', serif;
    font-size: 2.5rem;
    color: #333333;
    margin-bottom: 25px;
}

.feature-text p {
    color: #5D5D5D;
    line-height: 1.7;
    margin-bottom: 35px;
    font-size: 1.2rem;
}

.feature-section.layout-reversed .feature-content {
    flex-direction: row-reverse;
}

/* Hamburger Menu */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    height: 3px;
    width: 25px;
    background-color: #333;
    margin: 4px 0;
    transition: 0.4s;
}

/* Responsive Design */
@media (max-width: 992px) {
    .about-us-content, .feature-content {
        gap: 40px;
    }
    .focus-icon {
        width: 80px;
        height: 80px;
    }
    .focus-area-label {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    /* Navbar Responsive */
    .navbar {
        flex-direction: row; /* Keep logo and toggle inline */
        flex-wrap: wrap;
    }
    .navbar .menu {
        display: none;
        width: 100%;
        text-align: center;
        flex-direction: column;
    }
    .navbar .menu.active {
        display: flex; /* Show menu when active */
    }
    .menu-toggle {
        display: flex;
    }

    /* Hero Section Responsive */
    .hero {
        grid-template-columns: 1fr;
        padding: 2rem;
        text-align: center;
    }
    .hero-text {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    .hero-text h1 {
        font-size: 2rem;
    }
    .hero-text p {
        font-size: 1rem;
    }

    /* About Us Section Responsive */
    .about-us-section {
        padding: 60px 0;
        min-height: auto;
    }
    .about-us-content {
        flex-direction: column;
    }
    .about-us-left,
    .about-us-right {
        width: 100%;
    }
    .about-us-right {
        grid-template-columns: 1fr;
        margin-top: 40px;
    }
    .about-us-left {
        align-items: center;
        text-align: center;
    }
    .about-us-button {
        align-self: center;
    }
    .focus-area-item {
        flex-direction: row; /* Ensure items are rows */
        justify-content: flex-start; /* Align to the start */
        gap: 20px; /* Consistent gap */
    }

    /* Feature Sections Responsive */
    .feature-section {
        padding: 60px 0;
        min-height: auto;
    }
    .feature-content {
        flex-direction: column;
    }
    .feature-section.layout-reversed .feature-content {
        flex-direction: column-reverse;
    }
    .feature-text {
        text-align: center;
    }
    .feature-text .btn {
        align-self: center;
    }
}


* { box-sizing: border-box; }

/* SEVO Theme Variables for Posts */
:root {
    --sevo-brown: #5A3A28;
    --sevo-cream: #F5EFE6;
    --sevo-orange: #FF8C42;
    --sevo-green: #16A34A;
    --sevo-blue: #2563EB;
    --post-shadow: rgba(90, 58, 40, 0.15);
}
.container {
    max-width: 1280px;
    margin: 3rem auto;
    padding: 1rem;
}

h1 {
    text-align: center;
}

/* CSS Variables for consistent theming */
:root {
    --text-color: #333333;
    --primary-color: #c6895c;
    --accent-color: #5a3e2a;
    --line-height: 1.6;
}

button, .btn {
    background-color: #fcebd8;
    color: #c6895c;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    transition: transform 0.3s ease, background-color 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    outline: 0;
    font-size: 1rem;
}

button:hover, .btn:hover {
    transform: scale(1.05);
    background-color: #ffffff;
    color: #b57a50;
}

button .fa, .btn .fa {
    font-size: 0.75em;
    margin-left: 0.5em;
}

button.btn--primary, .btn--primary {
    background-color: #fcebd8;
    color: #c6895c;
    border: 2px solid #c6895c;
}

button.btn--primary:hover, .btn--primary:hover {
    transform: scale(1.05);
    background-color: #c6895c;
    color: #ffffff;
}

button.btn--accent, .btn--accent {
    background-color: #fcebd8;
    color: #c6895c;
    border: 2px solid #c6895c;
}

button.btn--accent:hover, .btn--accent:hover {
    transform: scale(1.05);
    background-color: #c6895c;
    color: #ffffff;
}

.posts {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 2.5rem;
    
    @media(max-width: 1140px) {
        grid-template-columns: repeat(2, 1fr);
    }
    
    @media(max-width: 768px) {
        grid-template-columns: repeat(1, 1fr);
    }
}


/* Post card layout variables converted to CSS custom properties */
:root {
    --excerpt-height: 8rem; /* 5rem * 1.6 line-height */
    --content-height: 16.85rem; /* calculated height for expanded content */
    --content-height-collapsed: 5.55rem; /* calculated height for collapsed content */
    --content-overlap-collapsed: 3rem;
    --content-overlap: 9.9rem; /* calculated overlap for expanded state */
}

/* Post card styles converted from SCSS to CSS */
.post__image {
    width: 100%;
    height: 240px;
    position: relative;
    overflow: hidden;
}

.post__image:before,
.post__image:after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
}

/* Image background */
.post__image:before {
    background-size: cover;
    background-position: center center;
    transform: scale(1);
    filter: blur(0);
    transition: 2s cubic-bezier(0, 0.6, 0.2, 1);
}

/* Overlay - SEVO Theme */
.post__image:after {
    background: linear-gradient(30deg, var(--sevo-brown) 0%, var(--sevo-orange) 100%);
    background-size: 100% 300%;
    background-position: bottom left;
    opacity: 0.15;
    transition: 2s cubic-bezier(0, 0.6, 0.2, 1);
}

/* Dynamic image support - when background-image is set inline */
.post__image[style*="background-image"]:before {
    background-image: inherit !important;
}

/* Fallback image variants for static content */
.post__image--1:before { background-image: url('https://images.unsplash.com/photo-1510951459752-aac634df6e86?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=50bdf8b5068e794a82c849cc7e269ed3'); }
.post__image--2:before { background-image: url('https://images.unsplash.com/photo-1529392960549-df4af50eac23?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=b482040f9d3a25a5e5352948f68f3a0e'); }
.post__image--3:before { background-image: url('https://images.unsplash.com/photo-1506258998-82810ddc75a3?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=68da264c24bc024a0b2ff92c349e89ed'); }
.post__image--4:before { background-image: url('https://images.unsplash.com/photo-1520875777965-f99b03dc86e8?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=34ff37a297e7e9e7be972356103b6750'); }
.post__image--5:before { background-image: url('https://images.unsplash.com/photo-1527664557558-a2b352fcf203?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=d06ac80d600822cb010987a6af4ff02a'); }
.post__image--6:before { background-image: url('https://images.unsplash.com/photo-1507679799987-c73779587ccf?h=240&ixlib=rb-0.3.5&q=85&fm=jpg&crop=entropy&cs=srgb&ixid=eyJhcHBfaWQiOjE0NTg5fQ&s=f982b6bf45d8a72d038b60a010e16767'); }

.post__content {
    margin: calc(-1 * var(--content-overlap-collapsed)) 1.5rem 0;
    padding: 2rem;
    background-color: var(--sevo-cream);
    border-radius: 12px;
    box-shadow: 0 1rem 3rem var(--post-shadow);
    transition: all 0.3s ease-in-out;
    position: relative;
    z-index: 1;
    user-select: none;
    border: 1px solid rgba(90, 58, 40, 0.1);
}

.post__inside {
    overflow: hidden;
    height: var(--content-height-collapsed);
    transition: height 0.2s ease-in-out;
}

.post__date {
    color: var(--sevo-orange);
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.post__title {
    font-size: 1.5rem;
    line-height: 1.3;
    margin: 0 0 1rem;
    font-weight: 600;
    color: var(--sevo-brown);
    font-family: 'Inter', sans-serif;
}

.post__excerpt {
    overflow: hidden;
    margin: 0;
    max-height: var(--excerpt-height);
    position: relative;
    color: #666666;
    line-height: 1.6;
    font-size: 0.95rem;
}

.post__button {
    margin-top: 1.5rem;
    background-color: #fcebd8;
    color: #c6895c;
    border: 2px solid transparent;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    font-weight: bold;
    font-size: 1rem;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;

    /* --- Default Hidden State --- */
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);

    /* --- Smooth Transition --- */
    transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s ease, background-color 0.3s ease;
}

.post__button:hover {
    background-color: #ffffff;
    color: #b57a50;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(90, 58, 40, 0.3);
}

/* ====== HOVER EFFECTS - SEVO Theme ====== */

.post:hover .post__content {
    margin-top: calc(-1 * var(--content-overlap));
    background-color: #ffffff;
    box-shadow: 0 2rem 4rem var(--post-shadow);
    transform: translateY(-5px);
}

.post:hover .post__inside {
    height: var(--content-height);
}

.post:hover .post__image:after {
    opacity: 0.3;
    background: linear-gradient(30deg, var(--sevo-brown) 0%, var(--sevo-green) 100%);
}

.post:hover .post__image:before {
    transform: scale(1.1);
    filter: blur(2px);
}

.post:hover .post__title {
    color: var(--sevo-orange);
}

/* --- This is the corrected hover rule for the button --- */
.post:hover .post__button {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Ensure the button is properly positioned within the card */
.post__inside {
    position: relative;
}

/* Make sure the entire post card is hoverable */
.post {
    cursor: pointer;
    position: relative;
}

/* Ensure all buttons have consistent hero button styling */
a.btn, input[type="submit"], input[type="button"], .button {
    background-color: #fcebd8;
    color: #c6895c;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    transition: transform 0.3s ease, background-color 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
    outline: 0;
    font-size: 1rem;
}

a.btn:hover, input[type="submit"]:hover, input[type="button"]:hover, .button:hover {
    transform: scale(1.05);
    background-color: #ffffff;
    color: #b57a50;
}

/* Loading animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hide loading state when posts are loaded */
.posts:not(:empty) .loading-state {
    display: none;
}

/* Stats Section Responsive */
@media (max-width: 768px) {
    .stats-section .container {
        padding: 0 20px !important;
    }

    .stats-section h2 {
        font-size: 2rem !important;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
        gap: 20px !important;
    }
}

/* Footer Responsive */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr !important;
        gap: 30px !important;
    }

    .footer-bottom {
        flex-direction: column !important;
        text-align: center !important;
        gap: 15px !important;
    }

    .footer-left {
        min-width: auto !important;
    }

    .footer-right {
        justify-content: center !important;
    }
}