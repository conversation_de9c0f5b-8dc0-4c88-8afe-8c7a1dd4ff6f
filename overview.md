AI Prompt: Debug, Refine, and Build a Comprehensive "About Us" Page

Your Role:
You are an expert front-end developer and CSS debugger. Your task is to analyze, fix, and build a new "About Us" page, ensuring it is visually consistent with an existing website theme and incorporates dynamic, user-friendly components.

Primary Goal:
Create the complete HTML, CSS, and necessary JavaScript for the about_us.html file. This involves not only building new sections but also fixing specific existing issues related to styling and component functionality, as detailed below.
Context & Core Requirements

    Target File: The work should be done for the file located at pages/en/about_us.html.

    Reusable Components: The navbar and footer for this page should be identical to the ones in index.html.

    Theme Consistency & Debugging:

        Problem: The font sizes on the current about_us.html are inconsistent with index.html (e.g., the main "About Us" heading is too small).

        Task: All new and existing elements must strictly match the visual identity (colors, button styles, and especially typography) of index.html. Before building new sections, ensure a global CSS rule or utility class is in place to make all page headings (<h1>, <h2>, etc.) consistent across the entire site.

Page Structure & Content (In Order)

1. Hero Section:

    Layout: A two-column grid layout.

    Left Column (Text): An <h1> heading ("Our Story, Our Mission") and an introductory <p>.

    Right Column (Image): A high-quality photo with a 16:9 aspect ratio.

2. Interactive Timeline of Achievements Section:

    Concept: This section must be a dynamic, interactive component controlled by JavaScript, not a static vertical list.

    Layout:

        A main display area that shows one achievement at a time.

        Navigation controls: "Previous" and "Next" arrow buttons (<button>).

        A visual indicator of progress, such as a horizontal line with dots representing the years. The dot for the currently active year should be highlighted.

    Functionality:

        Problem 1: The current timeline just scrolls through years.

        Task: When a user clicks the "Next" arrow, the component must transition to display the next achievement in the sequence (updating the year, title, and description). The "Previous" arrow should do the reverse. The highlighted year on the progress indicator must also update accordingly.

    Content & Structure:

        Problem 2: Long descriptions get cut off.

        Task: The container for the event description must dynamically adjust its height to fully display all text, no matter how long. Text must never be truncated or overflow its container.

        Problem 3: Unwanted numbers or bullet points are appearing above the event title.

        Task: Identify the source of these stray list markers. This is likely an <ol> or <ul> tag being used for the event content. Apply CSS to remove all default list styling (list-style-type: none; and padding: 0; margin: 0;) from the relevant container to ensure a clean presentation.